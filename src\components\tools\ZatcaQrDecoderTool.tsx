'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { QrCode, Unlock } from 'lucide-react';

interface ZATCAData {
  sellerName: string;
  sellerVAT: string;
  dateTime: string;
  total: string;
  vatAmount: string;
}

export function ZatcaQrDecoderTool() {
  const [base64Input, setBase64Input] = useState('');
  const [decodedData, setDecodedData] = useState<ZATCAData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const decodeZATCAQR = (base64Data: string): ZATCAData | null => {
    try {
      // Clean the input - remove any whitespace or newlines
      const cleanBase64 = base64Data.trim().replace(/\s/g, '');

      // Decode Base64 to binary
      const binaryString = atob(cleanBase64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      console.log('Decoded bytes:', Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join(' '));

      // Parse TLV data
      const result: Partial<ZATCAData> = {};
      let offset = 0;

      while (offset < bytes.length) {
        if (offset + 2 > bytes.length) break;

        const tag = bytes[offset];
        const length = bytes[offset + 1];
        offset += 2;

        console.log(`Tag: ${tag}, Length: ${length}, Offset: ${offset}`);

        if (offset + length > bytes.length) {
          console.warn(`Invalid length: ${length} at offset ${offset}, remaining bytes: ${bytes.length - offset}`);
          break;
        }

        const valueBytes = bytes.slice(offset, offset + length);
        const value = new TextDecoder('utf-8').decode(valueBytes);
        offset += length;

        console.log(`Tag ${tag}: "${value}"`);

        switch (tag) {
          case 1:
            result.sellerName = value;
            break;
          case 2:
            result.sellerVAT = value;
            break;
          case 3:
            result.dateTime = value;
            break;
          case 4:
            result.total = value;
            break;
          case 5:
            result.vatAmount = value;
            break;
          default:
            console.warn(`Unknown tag: ${tag}`);
        }
      }

      console.log('Parsed result:', result);

      // Check if we have at least the essential fields
      if (result.sellerName || result.sellerVAT || result.dateTime || result.total || result.vatAmount) {
        return result as ZATCAData;
      }

      return null;
    } catch (err) {
      console.error('Decoding error:', err);
      return null;
    }
  };

  const handleDecode = () => {
    setError(null);
    setDecodedData(null);

    if (!base64Input.trim()) {
      setError('يرجى إدخال البيانات المُشفرة');
      return;
    }

    console.log('Input Base64:', base64Input.trim());

    const decoded = decodeZATCAQR(base64Input.trim());
    if (decoded) {
      setDecodedData(decoded);
      console.log('Successfully decoded:', decoded);
    } else {
      setError('فشل في فك تشفير البيانات. تحقق من وحدة التحكم (Console) للمزيد من التفاصيل. تأكد من أن البيانات صحيحة ومتوافقة مع معايير ZATCA');
    }
  };

  const formatDateTime = (dateTime: string) => {
    try {
      const date = new Date(dateTime);
      return date.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateTime;
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          فك تشفير QR Code - ZATCA
        </CardTitle>
        <CardDescription>
          أداة لفك تشفير رموز QR الخاصة بالفواتير الإلكترونية المتوافقة مع هيئة الزكاة والضريبة والجمارك
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="base64-input">البيانات المُشفرة (Base64)</Label>
          <Textarea
            id="base64-input"
            placeholder="الصق هنا البيانات المُشفرة من QR Code..."
            value={base64Input}
            onChange={(e) => setBase64Input(e.target.value)}
            className="min-h-[120px] font-mono text-sm"
          />
        </div>

        <Button onClick={handleDecode} className="w-full" size="lg">
          <Unlock className="ml-2 h-4 w-4" />
          فك التشفير
        </Button>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {decodedData && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">البيانات المستخرجة:</h3>

            <div className="grid gap-4">
              {decodedData.sellerName && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <Label className="text-sm font-medium text-blue-700">اسم البائع (Tag 1)</Label>
                  <p className="text-blue-900 mt-1">{decodedData.sellerName}</p>
                </div>
              )}

              {decodedData.sellerVAT && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <Label className="text-sm font-medium text-green-700">الرقم الضريبي (Tag 2)</Label>
                  <p className="text-green-900 mt-1 font-mono">{decodedData.sellerVAT}</p>
                </div>
              )}

              {decodedData.dateTime && (
                <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                  <Label className="text-sm font-medium text-purple-700">تاريخ ووقت الفاتورة (Tag 3)</Label>
                  <p className="text-purple-900 mt-1">{formatDateTime(decodedData.dateTime)}</p>
                  <p className="text-purple-600 text-sm mt-1">({decodedData.dateTime})</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {decodedData.total && (
                  <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <Label className="text-sm font-medium text-orange-700">المجموع الإجمالي (Tag 4)</Label>
                    <p className="text-orange-900 mt-1 text-lg font-semibold">{decodedData.total} ريال</p>
                  </div>
                )}

                {decodedData.vatAmount && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <Label className="text-sm font-medium text-yellow-700">ضريبة القيمة المضافة (Tag 5)</Label>
                    <p className="text-yellow-900 mt-1 text-lg font-semibold">{decodedData.vatAmount} ريال</p>
                  </div>
                )}
              </div>
            </div>

            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <Label className="text-sm font-medium text-gray-700">ملاحظات</Label>
              <ul className="text-gray-600 text-sm mt-2 space-y-1">
                <li>• تم فك التشفير بنجاح وفقاً لمعايير ZATCA</li>
                <li>• البيانات متوافقة مع تنسيق TLV (Tag-Length-Value)</li>
                <li>• يمكن استخدام هذه البيانات للتحقق من صحة الفاتورة</li>
                <li>• تحقق من وحدة التحكم (Console) للمزيد من التفاصيل التقنية</li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
